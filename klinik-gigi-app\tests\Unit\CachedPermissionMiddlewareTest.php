<?php

namespace Tests\Unit;

use App\Http\Middleware\CachedPermissionMiddleware;
use App\Models\Permission;
use App\Models\Role;
use App\Models\User;
use App\Services\PermissionCacheService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Tests\TestCase;

class CachedPermissionMiddlewareTest extends TestCase
{
    use RefreshDatabase;

    protected CachedPermissionMiddleware $middleware;
    protected PermissionCacheService $permissionCache;
    protected User $user;
    protected Role $role;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->permissionCache = new PermissionCacheService();
        $this->middleware = new CachedPermissionMiddleware($this->permissionCache);
        
        // Create test role
        $this->role = Role::create([
            'nama_peran' => 'Test Role',
            'deskripsi' => 'Role untuk testing'
        ]);
        
        // Create test user
        $this->user = User::create([
            'username' => 'testuser',
            'nama_depan' => 'Test',
            'nama_belakang' => 'User',
            'email' => '<EMAIL>',
            'password' => 'password',
            'id_peran' => $this->role->id_peran,
            'aktif' => true
        ]);
        
        // Create test permissions
        Permission::create([
            'id_peran' => $this->role->id_peran,
            'modul' => 'users',
            'baca' => true,
            'tulis' => true,
            'ubah' => false,
            'hapus' => false
        ]);
    }

    public function test_middleware_allows_access_with_permission()
    {
        Auth::login($this->user);
        
        $request = Request::create('/test', 'GET');
        $next = function ($request) {
            return new Response('Success');
        };
        
        $response = $this->middleware->handle($request, $next, 'users', 'read');
        
        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals('Success', $response->getContent());
    }

    public function test_middleware_denies_access_without_permission()
    {
        Auth::login($this->user);
        
        $request = Request::create('/test', 'GET');
        $next = function ($request) {
            return new Response('Success');
        };
        
        $response = $this->middleware->handle($request, $next, 'users', 'delete');
        
        $this->assertEquals(302, $response->getStatusCode()); // Redirect
    }

    public function test_middleware_redirects_unauthenticated_user()
    {
        $request = Request::create('/test', 'GET');
        $next = function ($request) {
            return new Response('Success');
        };
        
        $response = $this->middleware->handle($request, $next, 'users', 'read');
        
        $this->assertEquals(302, $response->getStatusCode());
    }

    public function test_middleware_blocks_inactive_user()
    {
        // Make user inactive
        $this->user->update(['aktif' => false]);
        Auth::login($this->user);
        
        $request = Request::create('/test', 'GET');
        $next = function ($request) {
            return new Response('Success');
        };
        
        $response = $this->middleware->handle($request, $next, 'users', 'read');
        
        $this->assertEquals(302, $response->getStatusCode());
        $this->assertFalse(Auth::check()); // User should be logged out
    }

    public function test_middleware_returns_json_for_api_requests()
    {
        Auth::login($this->user);
        
        $request = Request::create('/api/test', 'GET');
        $request->headers->set('Accept', 'application/json');
        
        $next = function ($request) {
            return new Response('Success');
        };
        
        $response = $this->middleware->handle($request, $next, 'users', 'delete');
        
        $this->assertEquals(403, $response->getStatusCode());
        
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertArrayHasKey('error', $content);
        $this->assertArrayHasKey('required_permission', $content);
        $this->assertEquals('Forbidden', $content['error']);
    }

    public function test_middleware_with_nonexistent_module()
    {
        Auth::login($this->user);
        
        $request = Request::create('/test', 'GET');
        $next = function ($request) {
            return new Response('Success');
        };
        
        $response = $this->middleware->handle($request, $next, 'nonexistent', 'read');
        
        $this->assertEquals(302, $response->getStatusCode()); // Should redirect back
    }

    public function test_middleware_with_default_action()
    {
        Auth::login($this->user);
        
        $request = Request::create('/test', 'GET');
        $next = function ($request) {
            return new Response('Success');
        };
        
        // Test with default action (read)
        $response = $this->middleware->handle($request, $next, 'users');
        
        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals('Success', $response->getContent());
    }

    public function test_middleware_uses_cached_permissions()
    {
        Auth::login($this->user);
        
        // Pre-load permissions to cache
        $this->permissionCache->getUserPermissions($this->user->id);
        
        $request = Request::create('/test', 'GET');
        $next = function ($request) {
            return new Response('Success');
        };
        
        $response = $this->middleware->handle($request, $next, 'users', 'read');
        
        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals('Success', $response->getContent());
    }
}
