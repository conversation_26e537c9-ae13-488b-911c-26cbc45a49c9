import{r as a,j as e,H as o}from"./app-BI5U44id.js";import{A as l}from"./app-layout-BGu0uNax.js";/* empty css            */import"./app-logo-icon-BTT_CVoL.js";import"./index-EqmXnzIr.js";import"./index-D1AZekuB.js";function r({className:d}){const s=a.useId();return e.jsxs("svg",{className:d,fill:"none",children:[e.jsx("defs",{children:e.jsx("pattern",{id:s,x:"0",y:"0",width:"10",height:"10",patternUnits:"userSpaceOnUse",children:e.jsx("path",{d:"M-3 13 15-5M-5 5l18-18M-1 21 17 3"})})}),e.jsx("rect",{stroke:"none",fill:`url(#${s})`,width:"100%",height:"100%"})]})}const t=[{title:"Dashboard",href:"/dashboard"}];function h(){return e.jsxs(l,{breadcrumbs:t,children:[e.jsx(o,{title:"Dashboard"}),e.jsxs("div",{className:"flex h-full flex-1 flex-col gap-4 rounded-xl p-4 overflow-x-auto",children:[e.jsxs("div",{className:"grid auto-rows-min gap-4 md:grid-cols-3",children:[e.jsx("div",{className:"relative aspect-video overflow-hidden rounded-xl border border-sidebar-border/70 dark:border-sidebar-border",children:e.jsx(r,{className:"absolute inset-0 size-full stroke-neutral-900/20 dark:stroke-neutral-100/20"})}),e.jsx("div",{className:"relative aspect-video overflow-hidden rounded-xl border border-sidebar-border/70 dark:border-sidebar-border",children:e.jsx(r,{className:"absolute inset-0 size-full stroke-neutral-900/20 dark:stroke-neutral-100/20"})}),e.jsx("div",{className:"relative aspect-video overflow-hidden rounded-xl border border-sidebar-border/70 dark:border-sidebar-border",children:e.jsx(r,{className:"absolute inset-0 size-full stroke-neutral-900/20 dark:stroke-neutral-100/20"})})]}),e.jsx("div",{className:"relative min-h-[100vh] flex-1 overflow-hidden rounded-xl border border-sidebar-border/70 md:min-h-min dark:border-sidebar-border",children:e.jsx(r,{className:"absolute inset-0 size-full stroke-neutral-900/20 dark:stroke-neutral-100/20"})})]})]})}export{h as default};
