<?php

namespace App\Http\Controllers;

use App\Http\Requests\PasswordValidationRequest;
use App\Models\AuditLog;
use App\Models\Role;
use App\Models\User;
use App\Rules\NoMaliciousContent;
use App\Services\InputSanitizationService;
use App\Services\PasswordPolicyService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;
use Inertia\Inertia;

class UserController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $users = User::with('role')
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return Inertia::render('users/index', [
            'users' => $users
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $roles = Role::all();

        return Inertia::render('users/create', [
            'roles' => $roles
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Basic validation with security checks
        $validated = $request->validate([
            'username' => ['required', 'string', 'max:255', 'unique:users', new NoMaliciousContent()],
            'nama_depan' => ['required', 'string', 'max:255', new NoMaliciousContent()],
            'nama_belakang' => ['required', 'string', 'max:255', new NoMaliciousContent()],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users', new NoMaliciousContent()],
            'no_telepon' => ['nullable', 'string', 'max:20', new NoMaliciousContent()],
            'id_peran' => 'required|exists:roles,id_peran',
            'nomor_str' => ['nullable', 'string', 'max:50', new NoMaliciousContent()],
            'nomor_sip' => ['nullable', 'string', 'max:50', new NoMaliciousContent()],
            'expired_str' => 'nullable|date',
            'expired_sip' => 'nullable|date',
            'aktif' => 'boolean'
        ]);

        // Additional sanitization for specific fields
        $validated['username'] = InputSanitizationService::sanitizeUsername($validated['username']);
        $validated['email'] = InputSanitizationService::sanitizeEmail($validated['email']);
        if ($validated['no_telepon']) {
            $validated['no_telepon'] = InputSanitizationService::sanitizePhone($validated['no_telepon']);
        }

        // Validate password with policy
        $passwordValidation = $request->validate([
            'password' => 'required|string|confirmed'
        ]);

        $passwordErrors = PasswordPolicyService::validatePassword($passwordValidation['password']);
        if (!empty($passwordErrors)) {
            return back()->withErrors(['password' => $passwordErrors])->withInput();
        }

        $hashedPassword = Hash::make($passwordValidation['password']);
        $validated['password'] = $hashedPassword;
        $validated['name'] = $validated['nama_depan'] . ' ' . $validated['nama_belakang'];
        $validated['password_changed_at'] = now();

        $user = User::create($validated);

        // Save password to history
        $user->savePasswordHistory($hashedPassword);

        // Log user creation
        AuditLog::logUserManagement(
            AuditLog::ACTION_USER_CREATED,
            $user,
            null,
            $user->only(['username', 'nama_depan', 'nama_belakang', 'email', 'id_peran', 'aktif']),
            ['created_by' => auth()->user()?->full_name ?? 'System']
        );

        return redirect()->route('users.index')
            ->with('success', 'Pengguna berhasil ditambahkan.');
    }

    /**
     * Display the specified resource.
     */
    public function show(User $user)
    {
        $user->load('role');

        return Inertia::render('users/show', [
            'user' => $user
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(User $user)
    {
        $roles = Role::all();
        $user->load('role');

        return Inertia::render('users/edit', [
            'user' => $user,
            'roles' => $roles
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, User $user)
    {
        $validated = $request->validate([
            'username' => ['required', 'string', 'max:255', Rule::unique('users')->ignore($user->getKey()), new NoMaliciousContent()],
            'nama_depan' => ['required', 'string', 'max:255', new NoMaliciousContent()],
            'nama_belakang' => ['required', 'string', 'max:255', new NoMaliciousContent()],
            'email' => ['required', 'string', 'email', 'max:255', Rule::unique('users')->ignore($user->getKey()), new NoMaliciousContent()],
            'no_telepon' => ['nullable', 'string', 'max:20', new NoMaliciousContent()],
            'id_peran' => 'required|exists:roles,id_peran',
            'nomor_str' => ['nullable', 'string', 'max:50', new NoMaliciousContent()],
            'nomor_sip' => ['nullable', 'string', 'max:50', new NoMaliciousContent()],
            'expired_str' => 'nullable|date',
            'expired_sip' => 'nullable|date',
            'aktif' => 'boolean'
        ]);

        // Additional sanitization for specific fields
        $validated['username'] = InputSanitizationService::sanitizeUsername($validated['username']);
        $validated['email'] = InputSanitizationService::sanitizeEmail($validated['email']);
        if ($validated['no_telepon']) {
            $validated['no_telepon'] = InputSanitizationService::sanitizePhone($validated['no_telepon']);
        }

        // Handle password update if provided
        if ($request->filled('password')) {
            $passwordValidation = $request->validate([
                'password' => 'required|string|confirmed'
            ]);

            $passwordErrors = PasswordPolicyService::validatePassword($passwordValidation['password'], $user);
            if (!empty($passwordErrors)) {
                return back()->withErrors(['password' => $passwordErrors])->withInput();
            }

            $hashedPassword = Hash::make($passwordValidation['password']);
            $validated['password'] = $hashedPassword;
            $validated['password_changed_at'] = now();

            // Save password to history after update
            $user->savePasswordHistory($hashedPassword);
        }

        $validated['name'] = $validated['nama_depan'] . ' ' . $validated['nama_belakang'];

        // Store old values for audit
        $oldValues = $user->only(['username', 'nama_depan', 'nama_belakang', 'email', 'id_peran', 'aktif']);

        $user->update($validated);

        // Log user update
        AuditLog::logUserManagement(
            AuditLog::ACTION_USER_UPDATED,
            $user,
            $oldValues,
            $user->only(['username', 'nama_depan', 'nama_belakang', 'email', 'id_peran', 'aktif']),
            ['updated_by' => auth()->user()?->full_name ?? 'System']
        );

        return redirect()->route('users.index')
            ->with('success', 'Pengguna berhasil diperbarui.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(User $user)
    {
        // Prevent deleting own account
        if ($user->getKey() === auth()->user()?->getKey()) {
            return redirect()->back()
                ->with('error', 'Anda tidak dapat menghapus akun sendiri.');
        }

        // Store user data for audit before deletion
        $userData = $user->only(['username', 'nama_depan', 'nama_belakang', 'email', 'id_peran']);

        $user->delete();

        // Log user deletion
        AuditLog::logUserManagement(
            AuditLog::ACTION_USER_DELETED,
            $user,
            $userData,
            null,
            ['deleted_by' => auth()->user()?->full_name ?? 'System']
        );

        return redirect()->route('users.index')
            ->with('success', 'Pengguna berhasil dihapus.');
    }
}
