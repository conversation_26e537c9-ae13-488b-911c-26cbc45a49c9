<?php

namespace App\Http\Middleware;

use App\Services\QueryOptimizationService;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class N1DetectionMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Only monitor in development or when explicitly enabled
        if (!config('app.debug') && !config('database.detect_n1_problems', false)) {
            return $next($request);
        }

        // Start N+1 monitoring
        QueryOptimizationService::startN1Monitoring();

        $response = $next($request);

        // Analyze and log N+1 problems
        $problems = QueryOptimizationService::stopN1Monitoring();

        if (!empty($problems)) {
            $this->logN1Problems($request, $problems);
            
            // Add debug header in development
            if (config('app.debug')) {
                $response->headers->set('X-N1-Problems', count($problems));
            }
        }

        return $response;
    }

    /**
     * Log detected N+1 problems
     */
    protected function logN1Problems(Request $request, array $problems): void
    {
        foreach ($problems as $problem) {
            Log::warning('N+1 query problem detected', [
                'url' => $request->fullUrl(),
                'method' => $request->method(),
                'query_count' => $problem['count'],
                'total_time' => $problem['total_time'],
                'avg_time' => $problem['avg_time'],
                'severity' => $problem['severity'],
                'sql' => $problem['sql'],
                'suggestions' => $problem['suggestions']
            ]);
        }
    }
}
