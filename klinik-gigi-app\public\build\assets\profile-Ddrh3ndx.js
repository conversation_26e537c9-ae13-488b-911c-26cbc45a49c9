import{j as e,r as y,u as h,b,H as N,L as w}from"./app-BI5U44id.js";import{L as x,I as g,a as f}from"./label-DaCOvYC4.js";import{a as o,B as u}from"./app-logo-icon-BTT_CVoL.js";import{H as j,S as D}from"./layout-BNsEqIrn.js";import{R as C,T as k,C as P,a as v,X as S,b as z,D as F,P as E,O as _,A as L}from"./app-layout-BGu0uNax.js";import{z as T}from"./transition-Dwa4WIaI.js";/* empty css            */import"./index-D1AZekuB.js";import"./index-EqmXnzIr.js";function A({...a}){return e.jsx(C,{"data-slot":"dialog",...a})}function H({...a}){return e.jsx(k,{"data-slot":"dialog-trigger",...a})}function I({...a}){return e.jsx(E,{"data-slot":"dialog-portal",...a})}function O({...a}){return e.jsx(v,{"data-slot":"dialog-close",...a})}function R({className:a,...s}){return e.jsx(_,{"data-slot":"dialog-overlay",className:o("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/80",a),...s})}function U({className:a,children:s,...r}){return e.jsxs(I,{"data-slot":"dialog-portal",children:[e.jsx(R,{}),e.jsxs(P,{"data-slot":"dialog-content",className:o("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",a),...r,children:[s,e.jsxs(v,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[e.jsx(S,{}),e.jsx("span",{className:"sr-only",children:"Close"})]})]})]})}function q({className:a,...s}){return e.jsx("div",{"data-slot":"dialog-footer",className:o("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",a),...s})}function B({className:a,...s}){return e.jsx(z,{"data-slot":"dialog-title",className:o("text-lg leading-none font-semibold",a),...s})}function M({className:a,...s}){return e.jsx(F,{"data-slot":"dialog-description",className:o("text-muted-foreground text-sm",a),...s})}function W(){const a=y.useRef(null),{data:s,setData:r,delete:n,processing:l,reset:i,errors:d,clearErrors:m}=h({password:""}),p=t=>{t.preventDefault(),n(route("profile.destroy"),{preserveScroll:!0,onSuccess:()=>c(),onError:()=>a.current?.focus(),onFinish:()=>i()})},c=()=>{m(),i()};return e.jsxs("div",{className:"space-y-6",children:[e.jsx(j,{title:"Delete account",description:"Delete your account and all of its resources"}),e.jsxs("div",{className:"space-y-4 rounded-lg border border-red-100 bg-red-50 p-4 dark:border-red-200/10 dark:bg-red-700/10",children:[e.jsxs("div",{className:"relative space-y-0.5 text-red-600 dark:text-red-100",children:[e.jsx("p",{className:"font-medium",children:"Warning"}),e.jsx("p",{className:"text-sm",children:"Please proceed with caution, this cannot be undone."})]}),e.jsxs(A,{children:[e.jsx(H,{asChild:!0,children:e.jsx(u,{variant:"destructive",children:"Delete account"})}),e.jsxs(U,{children:[e.jsx(B,{children:"Are you sure you want to delete your account?"}),e.jsx(M,{children:"Once your account is deleted, all of its resources and data will also be permanently deleted. Please enter your password to confirm you would like to permanently delete your account."}),e.jsxs("form",{className:"space-y-6",onSubmit:p,children:[e.jsxs("div",{className:"grid gap-2",children:[e.jsx(x,{htmlFor:"password",className:"sr-only",children:"Password"}),e.jsx(g,{id:"password",type:"password",name:"password",ref:a,value:s.password,onChange:t=>r("password",t.target.value),placeholder:"Password",autoComplete:"current-password"}),e.jsx(f,{message:d.password})]}),e.jsxs(q,{className:"gap-2",children:[e.jsx(O,{asChild:!0,children:e.jsx(u,{variant:"secondary",onClick:c,children:"Cancel"})}),e.jsx(u,{variant:"destructive",disabled:l,asChild:!0,children:e.jsx("button",{type:"submit",children:"Delete account"})})]})]})]})]})]})]})}const X=[{title:"Profile settings",href:"/settings/profile"}];function ae({mustVerifyEmail:a,status:s}){const{auth:r}=b().props,{data:n,setData:l,patch:i,errors:d,processing:m,recentlySuccessful:p}=h({name:r.user.name,email:r.user.email}),c=t=>{t.preventDefault(),i(route("profile.update"),{preserveScroll:!0})};return e.jsxs(L,{breadcrumbs:X,children:[e.jsx(N,{title:"Profile settings"}),e.jsxs(D,{children:[e.jsxs("div",{className:"space-y-6",children:[e.jsx(j,{title:"Profile information",description:"Update your name and email address"}),e.jsxs("form",{onSubmit:c,className:"space-y-6",children:[e.jsxs("div",{className:"grid gap-2",children:[e.jsx(x,{htmlFor:"name",children:"Name"}),e.jsx(g,{id:"name",className:"mt-1 block w-full",value:n.name,onChange:t=>l("name",t.target.value),required:!0,autoComplete:"name",placeholder:"Full name"}),e.jsx(f,{className:"mt-2",message:d.name})]}),e.jsxs("div",{className:"grid gap-2",children:[e.jsx(x,{htmlFor:"email",children:"Email address"}),e.jsx(g,{id:"email",type:"email",className:"mt-1 block w-full",value:n.email,onChange:t=>l("email",t.target.value),required:!0,autoComplete:"username",placeholder:"Email address"}),e.jsx(f,{className:"mt-2",message:d.email})]}),a&&r.user.email_verified_at===null&&e.jsxs("div",{children:[e.jsxs("p",{className:"-mt-4 text-sm text-muted-foreground",children:["Your email address is unverified."," ",e.jsx(w,{href:route("verification.send"),method:"post",as:"button",className:"text-foreground underline decoration-neutral-300 underline-offset-4 transition-colors duration-300 ease-out hover:decoration-current! dark:decoration-neutral-500",children:"Click here to resend the verification email."})]}),s==="verification-link-sent"&&e.jsx("div",{className:"mt-2 text-sm font-medium text-green-600",children:"A new verification link has been sent to your email address."})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(u,{disabled:m,children:"Save"}),e.jsx(T,{show:p,enter:"transition ease-in-out",enterFrom:"opacity-0",leave:"transition ease-in-out",leaveTo:"opacity-0",children:e.jsx("p",{className:"text-sm text-neutral-600",children:"Saved"})})]})]})]}),e.jsx(W,{})]})]})}export{ae as default};
