import{g as p,e as u,r as f,j as l}from"./app-BI5U44id.js";import{b as v}from"./app-logo-icon-BTT_CVoL.js";var e=u();const h=p(e);var d=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],D=d.reduce((r,t)=>{const s=v(`Primitive.${t}`),i=f.forwardRef((o,a)=>{const{asChild:m,...n}=o,c=m?s:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),l.jsx(c,{...n,ref:a})});return i.displayName=`Primitive.${t}`,{...r,[t]:i}},{});function w(r,t){r&&e.flushSync(()=>r.dispatchEvent(t))}export{D as P,h as R,w as d,e as r};
