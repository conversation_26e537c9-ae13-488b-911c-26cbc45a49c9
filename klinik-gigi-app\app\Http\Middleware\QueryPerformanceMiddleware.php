<?php

namespace App\Http\Middleware;

use App\Services\QueryPerformanceService;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class QueryPerformanceMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Start query monitoring for this request
        if (config('app.debug') || config('database.monitor_queries', false)) {
            QueryPerformanceService::startMonitoring();
        }

        return $next($request);
    }
}
