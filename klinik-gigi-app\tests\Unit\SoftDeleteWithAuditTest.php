<?php

namespace Tests\Unit;

use App\Models\Patient;
use App\Models\AuditLog;
use App\Models\Role;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Auth;
use Tests\TestCase;

class SoftDeleteWithAuditTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;
    protected Patient $patient;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test role
        $role = Role::create([
            'nama_peran' => 'Test Role',
            'deskripsi' => 'Role untuk testing'
        ]);
        
        // Create test user
        $this->user = User::create([
            'username' => 'testuser',
            'nama_depan' => 'Test',
            'nama_belakang' => 'User',
            'email' => '<EMAIL>',
            'password' => 'password',
            'id_peran' => $role->id_peran,
            'aktif' => true
        ]);
        
        // Create test patient
        $this->patient = Patient::create([
            'nomor_pasien' => 'P001',
            'nama_depan' => 'John',
            'nama_belakang' => 'Doe',
            'tanggal_lahir' => '1990-01-01',
            'jenis_kelamin' => 'L',
            'aktif' => true
        ]);
        
        Auth::login($this->user);
    }

    public function test_soft_delete_creates_audit_log()
    {
        $this->patient->softDeleteWithReason('Test deletion');
        
        $this->assertSoftDeleted($this->patient);
        
        // Check audit log was created
        $auditLog = AuditLog::where('model_type', Patient::class)
                           ->where('model_id', $this->patient->id_pasien)
                           ->where('action', 'soft_delete')
                           ->first();
        
        $this->assertNotNull($auditLog);
        $this->assertEquals($this->user->id, $auditLog->additional_data['deleted_by']);
        $this->assertEquals('Test deletion', $auditLog->additional_data['reason']);
    }

    public function test_restore_creates_audit_log()
    {
        // First soft delete
        $this->patient->softDeleteWithReason('Test deletion');
        
        // Then restore
        $this->patient->restoreWithReason('Test restore');
        
        $this->assertNotSoftDeleted($this->patient);
        
        // Check restore audit log was created
        $auditLog = AuditLog::where('model_type', Patient::class)
                           ->where('model_id', $this->patient->id_pasien)
                           ->where('action', 'restore')
                           ->first();
        
        $this->assertNotNull($auditLog);
        $this->assertEquals($this->user->id, $auditLog->additional_data['restored_by']);
        $this->assertEquals('Test restore', $auditLog->additional_data['reason']);
    }

    public function test_force_delete_creates_audit_log()
    {
        // First soft delete
        $this->patient->softDeleteWithReason('Test deletion');
        
        // Then force delete
        $this->patient->forceDeleteWithReason('Test force delete');
        
        $this->assertDatabaseMissing('patients', ['id_pasien' => $this->patient->id_pasien]);
        
        // Check force delete audit log was created
        $auditLog = AuditLog::where('model_type', Patient::class)
                           ->where('model_id', $this->patient->id_pasien)
                           ->where('action', 'force_delete')
                           ->first();
        
        $this->assertNotNull($auditLog);
        $this->assertEquals($this->user->id, $auditLog->additional_data['force_deleted_by']);
        $this->assertEquals('Test force delete', $auditLog->additional_data['reason']);
    }

    public function test_can_be_deleted_without_dependencies()
    {
        $canDelete = $this->patient->canBeDeleted();
        
        $this->assertTrue($canDelete['can_delete']);
        $this->assertEmpty($canDelete['reasons']);
        $this->assertEmpty($canDelete['dependencies']);
    }

    public function test_safe_delete_success()
    {
        $result = $this->patient->safeDelete('Safe delete test');
        
        $this->assertTrue($result['success']);
        $this->assertEquals('Successfully deleted', $result['message']);
        $this->assertNotNull($result['deleted_at']);
        $this->assertSoftDeleted($this->patient);
    }

    public function test_batch_soft_delete()
    {
        // Create additional patients
        $patient2 = Patient::create([
            'nomor_pasien' => 'P002',
            'nama_depan' => 'Jane',
            'nama_belakang' => 'Doe',
            'tanggal_lahir' => '1992-01-01',
            'jenis_kelamin' => 'P',
            'aktif' => true
        ]);
        
        $patient3 = Patient::create([
            'nomor_pasien' => 'P003',
            'nama_depan' => 'Bob',
            'nama_belakang' => 'Smith',
            'tanggal_lahir' => '1988-01-01',
            'jenis_kelamin' => 'L',
            'aktif' => true
        ]);
        
        $ids = [$this->patient->id_pasien, $patient2->id_pasien, $patient3->id_pasien];
        $result = Patient::batchSoftDelete($ids, 'Batch delete test');
        
        $this->assertEquals(3, $result['deleted']);
        $this->assertEquals(0, count($result['failed']));
        $this->assertEquals(3, $result['total']);
        
        // Check all are soft deleted
        $this->assertSoftDeleted($this->patient);
        $this->assertSoftDeleted($patient2);
        $this->assertSoftDeleted($patient3);
    }

    public function test_batch_restore()
    {
        // Create and delete additional patients
        $patient2 = Patient::create([
            'nomor_pasien' => 'P002',
            'nama_depan' => 'Jane',
            'nama_belakang' => 'Doe',
            'tanggal_lahir' => '1992-01-01',
            'jenis_kelamin' => 'P',
            'aktif' => true
        ]);
        
        $this->patient->softDeleteWithReason('Test');
        $patient2->softDeleteWithReason('Test');
        
        $ids = [$this->patient->id_pasien, $patient2->id_pasien];
        $result = Patient::batchRestore($ids, 'Batch restore test');
        
        $this->assertEquals(2, $result['restored']);
        $this->assertEquals(0, count($result['failed']));
        $this->assertEquals(2, $result['total']);
        
        // Check all are restored
        $this->assertNotSoftDeleted($this->patient->fresh());
        $this->assertNotSoftDeleted($patient2->fresh());
    }

    public function test_get_deletion_history()
    {
        // Perform some operations
        $this->patient->softDeleteWithReason('First deletion');
        $this->patient->restoreWithReason('First restore');
        $this->patient->softDeleteWithReason('Second deletion');
        
        $history = $this->patient->getDeletionHistory();
        
        $this->assertCount(3, $history);
        
        // Check order (most recent first)
        $this->assertEquals('soft_delete', $history[0]->action);
        $this->assertEquals('restore', $history[1]->action);
        $this->assertEquals('soft_delete', $history[2]->action);
    }

    public function test_recently_deleted_scope()
    {
        // Create and delete a patient
        $this->patient->softDeleteWithReason('Recent deletion');
        
        $recentlyDeleted = Patient::recentlyDeleted(30)->get();
        $this->assertCount(1, $recentlyDeleted);
        $this->assertEquals($this->patient->id_pasien, $recentlyDeleted->first()->id_pasien);
    }

    public function test_old_deleted_scope()
    {
        // Create and delete a patient, then manually set old deletion date
        $this->patient->softDeleteWithReason('Old deletion');
        $this->patient->update(['deleted_at' => now()->subDays(100)]);
        
        $oldDeleted = Patient::oldDeleted(90)->get();
        $this->assertCount(1, $oldDeleted);
        $this->assertEquals($this->patient->id_pasien, $oldDeleted->first()->id_pasien);
    }

    public function test_cleanup_old_deleted()
    {
        // Create and delete patients with old deletion dates
        $patient2 = Patient::create([
            'nomor_pasien' => 'P002',
            'nama_depan' => 'Jane',
            'nama_belakang' => 'Doe',
            'tanggal_lahir' => '1992-01-01',
            'jenis_kelamin' => 'P',
            'aktif' => true
        ]);
        
        $this->patient->softDeleteWithReason('Old deletion 1');
        $patient2->softDeleteWithReason('Old deletion 2');
        
        // Set old deletion dates
        Patient::withTrashed()->whereIn('id_pasien', [$this->patient->id_pasien, $patient2->id_pasien])
               ->update(['deleted_at' => now()->subDays(100)]);
        
        $cleanedCount = Patient::cleanupOldDeleted(90);
        
        $this->assertEquals(2, $cleanedCount);
        
        // Check they are permanently deleted
        $this->assertDatabaseMissing('patients', ['id_pasien' => $this->patient->id_pasien]);
        $this->assertDatabaseMissing('patients', ['id_pasien' => $patient2->id_pasien]);
    }

    public function test_get_soft_delete_stats()
    {
        // Create additional patients
        $patient2 = Patient::create([
            'nomor_pasien' => 'P002',
            'nama_depan' => 'Jane',
            'nama_belakang' => 'Doe',
            'tanggal_lahir' => '1992-01-01',
            'jenis_kelamin' => 'P',
            'aktif' => true
        ]);
        
        // Delete one patient
        $this->patient->softDeleteWithReason('Test deletion');
        
        $stats = Patient::getSoftDeleteStats();
        
        $this->assertEquals(2, $stats['total']); // 2 total patients
        $this->assertEquals(1, $stats['active']); // 1 active patient
        $this->assertEquals(1, $stats['deleted']); // 1 deleted patient
    }
}
