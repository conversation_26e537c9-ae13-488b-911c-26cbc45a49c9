<?php

use App\Http\Controllers\SessionController;
use App\Http\Controllers\UserController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
    return Inertia::render('landing');
})->name('home');

Route::get('/welcome', function () {
    return Inertia::render('welcome');
})->name('welcome');

// Test route untuk debug
Route::get('/test-auth', function () {
    return response()->json([
        'authenticated' => auth()->check(),
        'user' => auth()->user(),
        'session_id' => session()->getId()
    ]);
});

Route::middleware(['auth'])->group(function () {
    Route::get('dashboard', function () {
        // Debug info
        $user = auth()->user();
        \Log::info('Dashboard accessed', [
            'user_id' => $user?->id,
            'user_name' => $user?->username,
            'session_id' => session()->getId(),
            'is_authenticated' => auth()->check()
        ]);

        return Inertia::render('dashboard');
    })->name('dashboard');

    // User Management Routes (temporarily disable permission middleware for testing)
    Route::resource('users', UserController::class);

    // Session Management Routes
    Route::prefix('api/session')->group(function () {
        Route::get('check', [SessionController::class, 'check'])->name('session.check');
        Route::post('extend', [SessionController::class, 'extend'])->name('session.extend');
        Route::get('active', [SessionController::class, 'activeSessions'])->name('session.active');
        Route::delete('terminate/{session_id}', [SessionController::class, 'terminateSession'])->name('session.terminate');
        Route::delete('terminate-others', [SessionController::class, 'terminateOtherSessions'])->name('session.terminate-others');
        Route::get('security-info', [SessionController::class, 'securityInfo'])->name('session.security-info');
        Route::post('heartbeat', [SessionController::class, 'heartbeat'])->name('session.heartbeat');
    });
});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
