<?php

namespace App\Traits;

use App\Services\QueryOptimizationService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Cache;

trait OptimizedQueries
{
    /**
     * Default relationships to eager load
     */
    protected array $defaultWith = [];

    /**
     * Cacheable query methods
     */
    protected array $cacheableQueries = [];

    /**
     * Boot the trait
     */
    public static function bootOptimizedQueries(): void
    {
        // Automatically eager load default relationships
        static::addGlobalScope('defaultWith', function (Builder $builder) {
            $instance = new static;
            if (!empty($instance->defaultWith)) {
                $builder->with($instance->defaultWith);
            }
        });
    }

    /**
     * Scope for optimized loading with common relationships
     */
    public function scopeWithOptimized(Builder $query, array $relations = []): Builder
    {
        $defaultRelations = $this->getDefaultRelations();
        $allRelations = array_merge($defaultRelations, $relations);
        
        return $query->with($allRelations);
    }

    /**
     * Get default relations for this model
     */
    protected function getDefaultRelations(): array
    {
        return $this->defaultWith ?? [];
    }

    /**
     * Cached query execution
     */
    public function scopeCached(Builder $query, string $key, int $ttl = 3600): Builder
    {
        $cacheKey = $this->getCacheKey($key);
        
        return $query->remember($ttl, $cacheKey);
    }

    /**
     * Batch load related models to avoid N+1
     */
    public static function batchLoadRelated(Collection $models, string $relation): Collection
    {
        if ($models->isEmpty()) {
            return $models;
        }

        // Get the relation instance
        $relationInstance = $models->first()->$relation();
        $foreignKey = $relationInstance->getForeignKeyName();
        $ownerKey = $relationInstance->getOwnerKeyName();

        // Get unique foreign key values
        $foreignKeys = $models->pluck($foreignKey)->unique()->filter();

        if ($foreignKeys->isEmpty()) {
            return $models;
        }

        // Load related models in batch
        $relatedModels = $relationInstance->getRelated()
            ->whereIn($ownerKey, $foreignKeys)
            ->get()
            ->keyBy($ownerKey);

        // Attach related models to original models
        foreach ($models as $model) {
            $relatedKey = $model->getAttribute($foreignKey);
            if ($relatedKey && $relatedModels->has($relatedKey)) {
                $model->setRelation($relation, $relatedModels->get($relatedKey));
            }
        }

        return $models;
    }

    /**
     * Load multiple relationships efficiently
     */
    public function scopeWithEfficient(Builder $query, array $relations): Builder
    {
        // Group relations by depth to optimize loading order
        $groupedRelations = $this->groupRelationsByDepth($relations);
        
        foreach ($groupedRelations as $depth => $depthRelations) {
            $query->with($depthRelations);
        }
        
        return $query;
    }

    /**
     * Group relations by their depth (nested level)
     */
    protected function groupRelationsByDepth(array $relations): array
    {
        $grouped = [];
        
        foreach ($relations as $relation) {
            $depth = substr_count($relation, '.');
            $grouped[$depth][] = $relation;
        }
        
        ksort($grouped);
        return $grouped;
    }

    /**
     * Paginate with optimized loading
     */
    public function scopePaginateOptimized(Builder $query, int $perPage = 15, array $relations = []): \Illuminate\Contracts\Pagination\LengthAwarePaginator
    {
        // Load default relations
        if (!empty($this->defaultWith)) {
            $query->with($this->defaultWith);
        }
        
        // Load additional relations
        if (!empty($relations)) {
            $query->with($relations);
        }
        
        return $query->paginate($perPage);
    }

    /**
     * Get cache key for this model
     */
    protected function getCacheKey(string $suffix = ''): string
    {
        $table = $this->getTable();
        $key = "model:{$table}";
        
        if ($suffix) {
            $key .= ":{$suffix}";
        }
        
        return $key;
    }

    /**
     * Invalidate model cache
     */
    public function invalidateCache(string $pattern = '*'): void
    {
        $cacheKey = $this->getCacheKey($pattern);
        QueryOptimizationService::invalidateCache($cacheKey);
    }

    /**
     * Cache expensive aggregation queries
     */
    public function scopeCachedCount(Builder $query, string $cacheKey = null, int $ttl = 3600): int
    {
        $key = $cacheKey ?? $this->getCacheKey('count');
        
        return QueryOptimizationService::cacheQuery($key, function () use ($query) {
            return $query->count();
        }, $ttl);
    }

    /**
     * Cache expensive sum queries
     */
    public function scopeCachedSum(Builder $query, string $column, string $cacheKey = null, int $ttl = 3600): float
    {
        $key = $cacheKey ?? $this->getCacheKey("sum:{$column}");
        
        return QueryOptimizationService::cacheQuery($key, function () use ($query, $column) {
            return $query->sum($column) ?? 0;
        }, $ttl);
    }

    /**
     * Load model with all necessary relationships for display
     */
    public function scopeForDisplay(Builder $query): Builder
    {
        $displayRelations = $this->getDisplayRelations();
        
        if (!empty($displayRelations)) {
            $query->with($displayRelations);
        }
        
        return $query;
    }

    /**
     * Get relationships needed for display
     * Override in child models
     */
    protected function getDisplayRelations(): array
    {
        return [];
    }

    /**
     * Load model with relationships needed for API responses
     */
    public function scopeForApi(Builder $query): Builder
    {
        $apiRelations = $this->getApiRelations();
        
        if (!empty($apiRelations)) {
            $query->with($apiRelations);
        }
        
        return $query;
    }

    /**
     * Get relationships needed for API responses
     * Override in child models
     */
    protected function getApiRelations(): array
    {
        return [];
    }

    /**
     * Efficient search scope with proper indexing
     */
    public function scopeSearch(Builder $query, string $term, array $columns = []): Builder
    {
        if (empty($term)) {
            return $query;
        }

        $searchColumns = !empty($columns) ? $columns : $this->getSearchableColumns();
        
        $query->where(function ($q) use ($term, $searchColumns) {
            foreach ($searchColumns as $column) {
                $q->orWhere($column, 'LIKE', "%{$term}%");
            }
        });
        
        return $query;
    }

    /**
     * Get searchable columns for this model
     * Override in child models
     */
    protected function getSearchableColumns(): array
    {
        return ['name', 'title', 'description'];
    }

    /**
     * Scope for active records (if model has 'aktif' column)
     */
    public function scopeActive(Builder $query): Builder
    {
        if (in_array('aktif', $this->getFillable()) || 
            array_key_exists('aktif', $this->getCasts())) {
            $query->where('aktif', true);
        }
        
        return $query;
    }

    /**
     * Scope for recent records
     */
    public function scopeRecent(Builder $query, int $days = 30): Builder
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }

    /**
     * Efficient exists check
     */
    public function scopeExistsOptimized(Builder $query): bool
    {
        return $query->select('id')->limit(1)->exists();
    }
}
