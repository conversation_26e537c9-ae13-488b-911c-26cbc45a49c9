<?php

namespace Tests\Unit;

use App\Models\Permission;
use App\Models\Role;
use App\Models\User;
use App\Services\PermissionCacheService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;

class PermissionCacheServiceTest extends TestCase
{
    use RefreshDatabase;

    protected PermissionCacheService $permissionCache;
    protected User $user;
    protected Role $role;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->permissionCache = new PermissionCacheService();
        
        // Create test role
        $this->role = Role::create([
            'nama_peran' => 'Test Role',
            'deskripsi' => 'Role untuk testing'
        ]);
        
        // Create test user
        $this->user = User::create([
            'username' => 'testuser',
            'nama_depan' => 'Test',
            'nama_belakang' => 'User',
            'email' => '<EMAIL>',
            'password' => 'password',
            'id_peran' => $this->role->id_peran,
            'aktif' => true
        ]);
        
        // Create test permissions
        Permission::create([
            'id_peran' => $this->role->id_peran,
            'modul' => 'users',
            'baca' => true,
            'tulis' => true,
            'ubah' => false,
            'hapus' => false
        ]);
        
        Permission::create([
            'id_peran' => $this->role->id_peran,
            'modul' => 'patients',
            'baca' => true,
            'tulis' => false,
            'ubah' => true,
            'hapus' => false
        ]);
    }

    public function test_get_user_permissions_from_database()
    {
        // Clear cache to ensure we're loading from database
        Cache::flush();
        
        $permissions = $this->permissionCache->getUserPermissions($this->user->id);
        
        $this->assertIsArray($permissions);
        $this->assertArrayHasKey('users', $permissions);
        $this->assertArrayHasKey('patients', $permissions);
        
        // Check users module permissions
        $this->assertTrue($permissions['users']['baca']);
        $this->assertTrue($permissions['users']['tulis']);
        $this->assertFalse($permissions['users']['ubah']);
        $this->assertFalse($permissions['users']['hapus']);
        
        // Check patients module permissions
        $this->assertTrue($permissions['patients']['baca']);
        $this->assertFalse($permissions['patients']['tulis']);
        $this->assertTrue($permissions['patients']['ubah']);
        $this->assertFalse($permissions['patients']['hapus']);
    }

    public function test_get_user_permissions_from_cache()
    {
        // First call loads from database and caches
        $permissions1 = $this->permissionCache->getUserPermissions($this->user->id);
        
        // Second call should load from cache
        $permissions2 = $this->permissionCache->getUserPermissions($this->user->id);
        
        $this->assertEquals($permissions1, $permissions2);
        
        // Verify cache key exists
        $cacheKey = PermissionCacheService::USER_PERMISSIONS_KEY . $this->user->id;
        $this->assertTrue(Cache::has($cacheKey));
    }

    public function test_has_permission()
    {
        // Test existing permissions
        $this->assertTrue($this->permissionCache->hasPermission($this->user->id, 'users', 'read'));
        $this->assertTrue($this->permissionCache->hasPermission($this->user->id, 'users', 'create'));
        $this->assertFalse($this->permissionCache->hasPermission($this->user->id, 'users', 'update'));
        $this->assertFalse($this->permissionCache->hasPermission($this->user->id, 'users', 'delete'));
        
        // Test non-existing module
        $this->assertFalse($this->permissionCache->hasPermission($this->user->id, 'nonexistent', 'read'));
        
        // Test invalid action
        $this->assertFalse($this->permissionCache->hasPermission($this->user->id, 'users', 'invalid'));
    }

    public function test_get_user_role()
    {
        $role = $this->permissionCache->getUserRole($this->user->id);
        
        $this->assertIsArray($role);
        $this->assertEquals($this->role->id_peran, $role['id_peran']);
        $this->assertEquals($this->role->nama_peran, $role['nama_peran']);
        $this->assertEquals($this->role->deskripsi, $role['deskripsi']);
    }

    public function test_get_user_modules()
    {
        $modules = $this->permissionCache->getUserModules($this->user->id);
        
        $this->assertIsArray($modules);
        $this->assertContains('users', $modules);
        $this->assertContains('patients', $modules);
        $this->assertCount(2, $modules);
    }

    public function test_invalidate_user_cache()
    {
        // Load permissions to cache
        $this->permissionCache->getUserPermissions($this->user->id);
        $this->permissionCache->getUserRole($this->user->id);
        
        // Verify cache exists
        $userPermissionKey = PermissionCacheService::USER_PERMISSIONS_KEY . $this->user->id;
        $userRoleKey = PermissionCacheService::USER_ROLE_KEY . $this->user->id;
        
        $this->assertTrue(Cache::has($userPermissionKey));
        $this->assertTrue(Cache::has($userRoleKey));
        
        // Invalidate cache
        $this->permissionCache->invalidateUserCache($this->user->id);
        
        // Verify cache is cleared
        $this->assertFalse(Cache::has($userPermissionKey));
        $this->assertFalse(Cache::has($userRoleKey));
    }

    public function test_invalidate_role_cache()
    {
        // Load permissions to cache
        $this->permissionCache->getRolePermissions($this->role->id_peran);
        $this->permissionCache->getUserPermissions($this->user->id);
        
        // Verify cache exists
        $rolePermissionKey = PermissionCacheService::ROLE_PERMISSIONS_KEY . $this->role->id_peran;
        $userPermissionKey = PermissionCacheService::USER_PERMISSIONS_KEY . $this->user->id;
        
        $this->assertTrue(Cache::has($rolePermissionKey));
        $this->assertTrue(Cache::has($userPermissionKey));
        
        // Invalidate role cache
        $this->permissionCache->invalidateRoleCache($this->role->id_peran);
        
        // Verify role cache and related user caches are cleared
        $this->assertFalse(Cache::has($rolePermissionKey));
        $this->assertFalse(Cache::has($userPermissionKey));
    }

    public function test_clear_all_cache()
    {
        // Load permissions to cache
        $this->permissionCache->getUserPermissions($this->user->id);
        $this->permissionCache->getRolePermissions($this->role->id_peran);
        
        // Clear all cache
        $this->permissionCache->clearAllCache();
        
        // Verify all caches are cleared
        $userPermissionKey = PermissionCacheService::USER_PERMISSIONS_KEY . $this->user->id;
        $rolePermissionKey = PermissionCacheService::ROLE_PERMISSIONS_KEY . $this->role->id_peran;
        
        $this->assertFalse(Cache::has($userPermissionKey));
        $this->assertFalse(Cache::has($rolePermissionKey));
    }

    public function test_user_without_role()
    {
        // Create user without role
        $userWithoutRole = User::create([
            'username' => 'noroleuser',
            'nama_depan' => 'No',
            'nama_belakang' => 'Role',
            'email' => '<EMAIL>',
            'password' => 'password',
            'id_peran' => null,
            'aktif' => true
        ]);
        
        $permissions = $this->permissionCache->getUserPermissions($userWithoutRole->id);
        $role = $this->permissionCache->getUserRole($userWithoutRole->id);
        
        $this->assertEmpty($permissions);
        $this->assertNull($role);
        $this->assertFalse($this->permissionCache->hasPermission($userWithoutRole->id, 'users', 'read'));
    }

    public function test_cache_stats()
    {
        // Load some data to cache
        $this->permissionCache->getUserPermissions($this->user->id);
        $this->permissionCache->getRolePermissions($this->role->id_peran);
        $this->permissionCache->getUserRole($this->user->id);
        
        $stats = $this->permissionCache->getCacheStats();
        
        $this->assertIsArray($stats);
        $this->assertArrayHasKey('user_permissions', $stats);
        $this->assertArrayHasKey('role_permissions', $stats);
        $this->assertArrayHasKey('user_roles', $stats);
        $this->assertArrayHasKey('total_cached_items', $stats);
        
        $this->assertGreaterThan(0, $stats['total_cached_items']);
    }
}
