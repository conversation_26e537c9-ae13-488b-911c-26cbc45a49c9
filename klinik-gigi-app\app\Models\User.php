<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use App\Traits\OptimizedQueries;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, OptimizedQueries;

    /**
     * Default relationships to eager load
     */
    protected array $defaultWith = ['role'];

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'username',
        'nama_depan',
        'nama_belakang',
        'email',
        'password',
        'no_telepon',
        'id_peran',
        'nomor_str',
        'nomor_sip',
        'expired_str',
        'expired_sip',
        'aktif',
        'login_terakhir',
        'password_changed_at',
        'failed_login_attempts',
        'locked_until',
        'last_password_change_reminder'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'expired_str' => 'date',
            'expired_sip' => 'date',
            'aktif' => 'boolean',
            'login_terakhir' => 'datetime',
            'password_changed_at' => 'datetime',
            'failed_login_attempts' => 'integer',
            'locked_until' => 'datetime',
            'last_password_change_reminder' => 'datetime'
        ];
    }

    /**
     * Relasi ke tabel roles
     */
    public function role(): BelongsTo
    {
        return $this->belongsTo(Role::class, 'id_peran', 'id_peran');
    }

    /**
     * Get full name attribute
     */
    public function getFullNameAttribute(): string
    {
        return trim($this->nama_depan . ' ' . $this->nama_belakang);
    }

    /**
     * Check if user has permission for specific module and action
     */
    public function hasPermission(string $module, string $action): bool
    {
        if (!$this->role) {
            return false;
        }

        return $this->role->hasPermission($module, $action);
    }

    /**
     * Check if user has role
     */
    public function hasRole(string $roleName): bool
    {
        return $this->role && $this->role->nama_peran === $roleName;
    }

    /**
     * Check if user is active
     */
    public function isActive(): bool
    {
        return $this->aktif;
    }

    /**
     * Check if STR is expired
     */
    public function isStrExpired(): bool
    {
        return $this->expired_str && $this->expired_str->isPast();
    }

    /**
     * Check if SIP is expired
     */
    public function isSipExpired(): bool
    {
        return $this->expired_sip && $this->expired_sip->isPast();
    }

    /**
     * Relationship to password histories
     */
    public function passwordHistories(): HasMany
    {
        return $this->hasMany(PasswordHistory::class);
    }

    /**
     * Update last login timestamp
     */
    public function updateLastLogin(): void
    {
        $this->update([
            'login_terakhir' => now(),
            'failed_login_attempts' => 0 // Reset failed attempts on successful login
        ]);
    }

    /**
     * Check if account is locked
     */
    public function isLocked(): bool
    {
        return $this->locked_until && $this->locked_until->isFuture();
    }

    /**
     * Lock account for specified duration
     */
    public function lockAccount(int $minutes = 60): void
    {
        $this->update([
            'locked_until' => now()->addMinutes($minutes),
            'failed_login_attempts' => 0
        ]);
    }

    /**
     * Unlock account
     */
    public function unlockAccount(): void
    {
        $this->update([
            'locked_until' => null,
            'failed_login_attempts' => 0
        ]);
    }

    /**
     * Increment failed login attempts
     */
    public function incrementFailedAttempts(): void
    {
        $this->increment('failed_login_attempts');
    }

    /**
     * Save password to history and update password_changed_at
     */
    public function savePasswordHistory(string $hashedPassword): void
    {
        // Save to password history
        $this->passwordHistories()->create([
            'password_hash' => $hashedPassword,
            'created_at' => now()
        ]);

        // Update password changed timestamp
        $this->update(['password_changed_at' => now()]);

        // Clean up old password histories
        PasswordHistory::cleanupOldPasswords($this->id, 5);
    }

    /**
     * Get relationships for display
     */
    protected function getDisplayRelations(): array
    {
        return ['role', 'role.permissions'];
    }

    /**
     * Get relationships for API responses
     */
    protected function getApiRelations(): array
    {
        return ['role'];
    }

    /**
     * Get searchable columns
     */
    protected function getSearchableColumns(): array
    {
        return ['username', 'nama_depan', 'nama_belakang', 'email'];
    }

    /**
     * Scope for active users with role
     */
    public function scopeActiveWithRole($query)
    {
        return $query->active()
                    ->with('role')
                    ->whereNotNull('id_peran');
    }

    /**
     * Scope for users by role
     */
    public function scopeByRole($query, string $roleName)
    {
        return $query->whereHas('role', function ($q) use ($roleName) {
            $q->where('nama_peran', $roleName);
        });
    }

    /**
     * Scope for users with expired credentials
     */
    public function scopeWithExpiredCredentials($query)
    {
        return $query->where(function ($q) {
            $q->where('expired_str', '<', now())
              ->orWhere('expired_sip', '<', now());
        });
    }

}
