import{r as u,u as h,j as s,H as g}from"./app-BI5U44id.js";import{L as n,I as d,a as p}from"./label-DaCOvYC4.js";import{A as j}from"./app-layout-BGu0uNax.js";import{S as v,H as _}from"./layout-BNsEqIrn.js";import{B as y}from"./app-logo-icon-BTT_CVoL.js";import{z as N}from"./transition-Dwa4WIaI.js";/* empty css            */import"./index-D1AZekuB.js";import"./index-EqmXnzIr.js";const C=[{title:"Password settings",href:"/settings/password"}];function R(){const i=u.useRef(null),c=u.useRef(null),{data:a,setData:e,errors:o,put:m,reset:t,processing:w,recentlySuccessful:f}=h({current_password:"",password:"",password_confirmation:""}),x=r=>{r.preventDefault(),m(route("password.update"),{preserveScroll:!0,onSuccess:()=>t(),onError:l=>{l.password&&(t("password","password_confirmation"),i.current?.focus()),l.current_password&&(t("current_password"),c.current?.focus())}})};return s.jsxs(j,{breadcrumbs:C,children:[s.jsx(g,{title:"Password settings"}),s.jsx(v,{children:s.jsxs("div",{className:"space-y-6",children:[s.jsx(_,{title:"Update password",description:"Ensure your account is using a long, random password to stay secure"}),s.jsxs("form",{onSubmit:x,className:"space-y-6",children:[s.jsxs("div",{className:"grid gap-2",children:[s.jsx(n,{htmlFor:"current_password",children:"Current password"}),s.jsx(d,{id:"current_password",ref:c,value:a.current_password,onChange:r=>e("current_password",r.target.value),type:"password",className:"mt-1 block w-full",autoComplete:"current-password",placeholder:"Current password"}),s.jsx(p,{message:o.current_password})]}),s.jsxs("div",{className:"grid gap-2",children:[s.jsx(n,{htmlFor:"password",children:"New password"}),s.jsx(d,{id:"password",ref:i,value:a.password,onChange:r=>e("password",r.target.value),type:"password",className:"mt-1 block w-full",autoComplete:"new-password",placeholder:"New password"}),s.jsx(p,{message:o.password})]}),s.jsxs("div",{className:"grid gap-2",children:[s.jsx(n,{htmlFor:"password_confirmation",children:"Confirm password"}),s.jsx(d,{id:"password_confirmation",value:a.password_confirmation,onChange:r=>e("password_confirmation",r.target.value),type:"password",className:"mt-1 block w-full",autoComplete:"new-password",placeholder:"Confirm password"}),s.jsx(p,{message:o.password_confirmation})]}),s.jsxs("div",{className:"flex items-center gap-4",children:[s.jsx(y,{disabled:w,children:"Save password"}),s.jsx(N,{show:f,enter:"transition ease-in-out",enterFrom:"opacity-0",leave:"transition ease-in-out",leaveTo:"opacity-0",children:s.jsx("p",{className:"text-sm text-neutral-600",children:"Saved"})})]})]})]})})]})}export{R as default};
