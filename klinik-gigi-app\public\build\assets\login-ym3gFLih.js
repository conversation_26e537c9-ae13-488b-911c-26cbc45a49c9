import{r as d,j as e,u as K,H as U}from"./app-BI5U44id.js";import{L as I,I as P,a as R}from"./label-DaCOvYC4.js";import{T as L}from"./text-link-D4dxTEat.js";import{c as X,u as F,a as $,B as J}from"./app-logo-icon-BTT_CVoL.js";import{c as Q,P as V,u as W,a as S,b as Y}from"./index-EqmXnzIr.js";import{P as N}from"./index-D1AZekuB.js";import{A as Z,L as ee}from"./auth-layout-BmCCpopn.js";/* empty css            *//**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const te=[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]],re=X("Check",te);function se(t){const s=d.useRef({value:t,previous:t});return d.useMemo(()=>(s.current.value!==t&&(s.current.previous=s.current.value,s.current.value=t),s.current.previous),[t])}var j="Checkbox",[oe,be]=Q(j),[ae,E]=oe(j);function ne(t){const{__scopeCheckbox:s,checked:o,children:n,defaultChecked:a,disabled:r,form:m,name:p,onCheckedChange:l,required:c,value:b="on",internal_do_not_use_render:h}=t,[f,v]=W({prop:o,defaultProp:a??!1,onChange:l,caller:j}),[k,C]=d.useState(null),[g,i]=d.useState(null),u=d.useRef(!1),w=k?!!m||!!k.closest("form"):!0,y={checked:f,disabled:r,setChecked:v,control:k,setControl:C,name:p,form:m,value:b,hasConsumerStoppedPropagationRef:u,required:c,defaultChecked:x(a)?!1:a,isFormControl:w,bubbleInput:g,setBubbleInput:i};return e.jsx(ae,{scope:s,...y,children:ce(h)?h(y):n})}var B="CheckboxTrigger",q=d.forwardRef(({__scopeCheckbox:t,onKeyDown:s,onClick:o,...n},a)=>{const{control:r,value:m,disabled:p,checked:l,required:c,setControl:b,setChecked:h,hasConsumerStoppedPropagationRef:f,isFormControl:v,bubbleInput:k}=E(B,t),C=F(a,b),g=d.useRef(l);return d.useEffect(()=>{const i=r?.form;if(i){const u=()=>h(g.current);return i.addEventListener("reset",u),()=>i.removeEventListener("reset",u)}},[r,h]),e.jsx(N.button,{type:"button",role:"checkbox","aria-checked":x(l)?"mixed":l,"aria-required":c,"data-state":z(l),"data-disabled":p?"":void 0,disabled:p,value:m,...n,ref:C,onKeyDown:S(s,i=>{i.key==="Enter"&&i.preventDefault()}),onClick:S(o,i=>{h(u=>x(u)?!0:!u),k&&v&&(f.current=i.isPropagationStopped(),f.current||i.stopPropagation())})})});q.displayName=B;var M=d.forwardRef((t,s)=>{const{__scopeCheckbox:o,name:n,checked:a,defaultChecked:r,required:m,disabled:p,value:l,onCheckedChange:c,form:b,...h}=t;return e.jsx(ne,{__scopeCheckbox:o,checked:a,defaultChecked:r,disabled:p,required:m,onCheckedChange:c,name:n,form:b,value:l,internal_do_not_use_render:({isFormControl:f})=>e.jsxs(e.Fragment,{children:[e.jsx(q,{...h,ref:s,__scopeCheckbox:o}),f&&e.jsx(H,{__scopeCheckbox:o})]})})});M.displayName=j;var T="CheckboxIndicator",A=d.forwardRef((t,s)=>{const{__scopeCheckbox:o,forceMount:n,...a}=t,r=E(T,o);return e.jsx(V,{present:n||x(r.checked)||r.checked===!0,children:e.jsx(N.span,{"data-state":z(r.checked),"data-disabled":r.disabled?"":void 0,...a,ref:s,style:{pointerEvents:"none",...t.style}})})});A.displayName=T;var D="CheckboxBubbleInput",H=d.forwardRef(({__scopeCheckbox:t,...s},o)=>{const{control:n,hasConsumerStoppedPropagationRef:a,checked:r,defaultChecked:m,required:p,disabled:l,name:c,value:b,form:h,bubbleInput:f,setBubbleInput:v}=E(D,t),k=F(o,v),C=se(r),g=Y(n);d.useEffect(()=>{const u=f;if(!u)return;const w=window.HTMLInputElement.prototype,_=Object.getOwnPropertyDescriptor(w,"checked").set,O=!a.current;if(C!==r&&_){const G=new Event("click",{bubbles:O});u.indeterminate=x(r),_.call(u,x(r)?!1:r),u.dispatchEvent(G)}},[f,C,r,a]);const i=d.useRef(x(r)?!1:r);return e.jsx(N.input,{type:"checkbox","aria-hidden":!0,defaultChecked:m??i.current,required:p,disabled:l,name:c,value:b,form:h,...s,tabIndex:-1,ref:k,style:{...s.style,...g,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});H.displayName=D;function ce(t){return typeof t=="function"}function x(t){return t==="indeterminate"}function z(t){return x(t)?"indeterminate":t?"checked":"unchecked"}function ie({className:t,...s}){return e.jsx(M,{"data-slot":"checkbox",className:$("peer border-input data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",t),...s,children:e.jsx(A,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:e.jsx(re,{className:"size-3.5"})})})}function ke({status:t,canResetPassword:s}){const{data:o,setData:n,post:a,processing:r,errors:m,reset:p}=K({email:"",password:"",remember:!1}),l=c=>{c.preventDefault(),a(route("login"),{onFinish:()=>p("password")})};return e.jsxs(Z,{title:"Log in to your account",description:"Enter your email and password below to log in",children:[e.jsx(U,{title:"Log in"}),e.jsxs("form",{className:"flex flex-col gap-6",onSubmit:l,children:[e.jsxs("div",{className:"grid gap-6",children:[e.jsxs("div",{className:"grid gap-2",children:[e.jsx(I,{htmlFor:"email",children:"Email address"}),e.jsx(P,{id:"email",type:"email",required:!0,autoFocus:!0,tabIndex:1,autoComplete:"email",value:o.email,onChange:c=>n("email",c.target.value),placeholder:"<EMAIL>"}),e.jsx(R,{message:m.email})]}),e.jsxs("div",{className:"grid gap-2",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(I,{htmlFor:"password",children:"Password"}),s&&e.jsx(L,{href:route("password.request"),className:"ml-auto text-sm",tabIndex:5,children:"Forgot password?"})]}),e.jsx(P,{id:"password",type:"password",required:!0,tabIndex:2,autoComplete:"current-password",value:o.password,onChange:c=>n("password",c.target.value),placeholder:"Password"}),e.jsx(R,{message:m.password})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(ie,{id:"remember",name:"remember",checked:o.remember,onClick:()=>n("remember",!o.remember),tabIndex:3}),e.jsx(I,{htmlFor:"remember",children:"Remember me"})]}),e.jsxs(J,{type:"submit",className:"mt-4 w-full",tabIndex:4,disabled:r,children:[r&&e.jsx(ee,{className:"h-4 w-4 animate-spin"}),"Log in"]})]}),e.jsxs("div",{className:"text-center text-sm text-muted-foreground",children:["Don't have an account?"," ",e.jsx(L,{href:route("register"),tabIndex:5,children:"Sign up"})]})]}),t&&e.jsx("div",{className:"mb-4 text-center text-sm font-medium text-green-600",children:t})]})}export{ke as default};
